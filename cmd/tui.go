package cmd

import (
	"fmt"

	"github.com/liyujun-dev/kui/internal/tui"
	"github.com/spf13/cobra"
)

// tuiCmd represents the tui command
var tuiCmd = &cobra.Command{
	Use:   "tui",
	Short: "启动 TUI 界面",
	Long: `启动交互式终端用户界面 (TUI)，提供可视化的 Kubernetes 资源管理体验。

使用方向键或 vim 风格的键盘快捷键进行导航:
• h,j,k,l - 左下上右移动
• q - 退出
• r - 刷新
• / - 搜索`,
	RunE: func(cmd *cobra.Command, args []string) error {
		if GetVerbose() {
			fmt.Println("启动 TUI 模式...")
		}

		app, err := tui.NewApp()
		if err != nil {
			return fmt.Errorf("创建 TUI 应用失败: %w", err)
		}

		if err := app.Run(); err != nil {
			return fmt.Errorf("运行 TUI 应用失败: %w", err)
		}

		return nil
	},
}

func init() {
	// TUI 特定的标志可以在这里添加
	// tuiCmd.Flags().StringP("namespace", "n", "", "指定命名空间")
}
