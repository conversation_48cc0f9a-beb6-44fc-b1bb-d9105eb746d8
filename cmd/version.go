package cmd

import (
	"fmt"
	"runtime"

	"github.com/spf13/cobra"
)

var (
	// 这些变量将在构建时通过 ldflags 注入
	version   = "dev"
	commit    = "unknown"
	buildTime = "unknown"
)

// versionCmd represents the version command
var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "显示版本信息",
	Long:  `显示 kui 的版本信息，包括版本号、构建时间和 Git 提交哈希。`,
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Printf("葵 (kui) 版本信息:\n")
		fmt.Printf("  版本:     %s\n", version)
		fmt.Printf("  提交:     %s\n", commit)
		fmt.Printf("  构建时间: %s\n", buildTime)
		fmt.Printf("  Go版本:   %s\n", runtime.Version())
		fmt.Printf("  系统:     %s/%s\n", runtime.GOOS, runtime.GOARCH)
	},
}
