package cmd

import (
	"github.com/spf13/cobra"
)

var (
	// 这些变量将在构建时通过 ldflags 注入
	version   = "dev"
	commit    = "unknown"
	buildTime = "unknown"
)

// versionCmd represents the version command
var versionCmd = &cobra.Command{
	Use:     "version",
	Aliases: []string{"v"},
	Short:   "显示版本信息",
	Long:    `显示 kui 的版本信息，包括版本号、构建时间和 Git 提交哈希。`,
	Run: func(cmd *cobra.Command, args []string) {
		showVersionInfo()
	},
}
