package cmd

import (
	"os"

	"github.com/spf13/cobra"
)

var (
	cfgFile string
	verbose bool
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "kui",
	Short: "葵 - Kubernetes TUI 管理工具",
	Long: `葵 (kui) 是一个基于终端的 Kubernetes 资源管理工具。
结合了 k9s 和 lens 的优点，为 DevOps 和运维人员提供直观的 TUI 界面。

特性:
• 实时资源浏览和监控
• 键盘快捷键操作
• 多集群支持
• 轻量级单一可执行文件`,
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() {
	err := rootCmd.Execute()
	if err != nil {
		os.Exit(1)
	}
}

func init() {
	// 全局标志
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "配置文件路径 (默认为 $HOME/.kui.yaml)")
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "详细输出")

	// 添加子命令
	rootCmd.AddCommand(versionCmd)
	rootCmd.AddCommand(tuiCmd)
}

// GetVerbose 返回 verbose 标志的值
func GetVerbose() bool {
	return verbose
}

// GetConfigFile 返回配置文件路径
func GetConfigFile() string {
	return cfgFile
}
