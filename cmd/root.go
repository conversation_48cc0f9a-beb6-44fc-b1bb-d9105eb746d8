package cmd

import (
	"fmt"
	"os"
	"runtime"

	"github.com/liyujun-dev/kui/internal/tui"
	"github.com/spf13/cobra"
)

var (
	cfgFile     string
	showVersion bool
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "kui",
	Short: "葵 - Kubernetes TUI 管理工具",
	Long: `葵 (kui) 是一个基于终端的 Kubernetes 资源管理工具。
结合了 k9s 和 lens 的优点，为 DevOps 和运维人员提供直观的 TUI 界面。

特性:
• 实时资源浏览和监控
• 键盘快捷键操作
• 多集群支持
• 轻量级单一可执行文件

使用方法:
  kui              启动 TUI 界面
  kui --help       显示帮助信息
  kui --version    显示版本信息`,
	RunE: func(cmd *cobra.Command, args []string) error {
		// 如果指定了 --version 标志，显示版本信息
		if showVersion {
			showVersionInfo()
			return nil
		}

		// 默认启动 TUI 界面
		if len(args) == 0 {
			return startTUI()
		}

		// 如果有参数但不是已知命令，显示帮助
		return cmd.Help()
	},
}

// Execute adds all child commands to the root command and sets flags appropriately.
// This is called by main.main(). It only needs to happen once to the rootCmd.
func Execute() {
	err := rootCmd.Execute()
	if err != nil {
		os.Exit(1)
	}
}

func init() {
	// 全局标志
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "配置文件路径 (默认为 $HOME/.kui.yaml)")
	rootCmd.Flags().BoolVarP(&showVersion, "version", "v", false, "显示版本信息")

	// 添加子命令 - 只保留 version 命令作为备用
	rootCmd.AddCommand(versionCmd)
}

// startTUI 启动 TUI 界面
func startTUI() error {
	app, err := tui.NewApp()
	if err != nil {
		return fmt.Errorf("创建 TUI 应用失败: %w", err)
	}

	if err := app.Run(); err != nil {
		return fmt.Errorf("运行 TUI 应用失败: %w", err)
	}

	return nil
}

// showVersionInfo 显示版本信息
func showVersionInfo() {
	fmt.Printf("葵 (kui) 版本信息:\n")
	fmt.Printf("  版本:     %s\n", version)
	fmt.Printf("  提交:     %s\n", commit)
	fmt.Printf("  构建时间: %s\n", buildTime)
	fmt.Printf("  Go版本:   %s\n", runtime.Version())
	fmt.Printf("  系统:     %s/%s\n", runtime.GOOS, runtime.GOARCH)
}

// GetConfigFile 返回配置文件路径
func GetConfigFile() string {
	return cfgFile
}
