# 葵 (kui) 使用指南

## 快速开始

### 1. 构建应用

```bash
# 克隆仓库
git clone https://github.com/liyujun-dev/kui.git
cd kui

# 构建
make build

# 或者直接运行
make tui
```

### 2. 基本使用

```bash
# 查看帮助
./kui --help

# 查看版本
./kui version

# 启动 TUI 界面
./kui tui

# 详细模式启动
./kui tui --verbose
```

## TUI 界面操作

### 导航

- **数字键 1-5**: 切换资源类型
  - `1`: Pods
  - `2`: Services  
  - `3`: Deployments
  - `4`: Nodes
  - `5`: Namespaces

- **方向键或 vim 键位**: 在列表中移动
  - `↑/k`: 向上移动
  - `↓/j`: 向下移动

### 操作

- `r`: 刷新当前资源列表
- `q` 或 `Ctrl+C`: 退出应用

### 界面布局

```
┌─────────────────────────────────────────────────────────────┐
│ 葵 (kui) - Kubernetes 资源管理                              │
├─────────────────────────────────────────────────────────────┤
│ [1] Pods  [2] Services  [3] Deployments  [4] Nodes  [5] NS  │
├─────────────────────────────────────────────────────────────┤
│ NAME                     READY    STATUS    RESTARTS   AGE  │
│ ─────────────────────────────────────────────────────────── │
│ > my-pod-1               1/1      Running   0          5m   │
│   my-pod-2               0/1      Pending   0          2m   │
│   my-pod-3               1/1      Running   2          1h   │
├─────────────────────────────────────────────────────────────┤
│ 命名空间: default | 资源数: 3 | 最后更新: 16:53:48 | 按 'r' 刷新, 'q' 退出 │
└─────────────────────────────────────────────────────────────┘
```

## 配置

### Kubeconfig

kui 使用标准的 Kubernetes 配置：

```bash
# 默认配置文件
~/.kube/config

# 通过环境变量指定
export KUBECONFIG=/path/to/your/kubeconfig

# 通过命令行参数指定
./kui --config /path/to/your/kubeconfig tui
```

### 多集群支持

kui 支持 kubeconfig 中的多个上下文：

```bash
# 查看当前上下文
kubectl config current-context

# 切换上下文
kubectl config use-context your-context-name

# 然后启动 kui
./kui tui
```

## 故障排除

### 常见问题

1. **无法连接到集群**
   - 检查 kubeconfig 文件是否存在且正确
   - 确认集群网络连接正常
   - 验证认证信息是否有效

2. **权限不足**
   - 确认当前用户有足够的 RBAC 权限
   - 检查 ServiceAccount 配置

3. **资源列表为空**
   - 确认当前命名空间中确实存在资源
   - 尝试切换到其他命名空间
   - 使用 `kubectl` 验证资源是否存在

### 调试模式

```bash
# 启用详细输出
./kui tui --verbose

# 查看具体错误信息
./kui tui 2>&1 | tee kui.log
```

## 开发模式

### 实时开发

```bash
# 开发模式运行（自动重新构建）
make dev

# 运行测试
make test

# 代码格式化
make fmt

# 代码检查
make vet
```

### 交叉编译

```bash
# 编译所有平台
make build-all

# 编译特定平台
make build-linux
make build-windows
make build-darwin
```

## 功能特性

### 当前支持的资源类型

- ✅ **Pods**: 显示状态、重启次数、节点信息
- ✅ **Services**: 显示类型、端口、选择器
- ✅ **Deployments**: 显示副本状态、更新策略
- ✅ **Nodes**: 显示节点状态、版本、角色
- ✅ **Namespaces**: 显示命名空间状态

### 计划中的功能

- 🔄 **实时监控**: 自动刷新资源状态
- 🔍 **搜索过滤**: 按名称、标签过滤资源
- 📝 **资源详情**: 查看完整的资源 YAML
- 📊 **资源监控**: CPU、内存使用情况
- 🔧 **资源操作**: 删除、编辑资源
- 📋 **日志查看**: Pod 日志实时查看
- 🌐 **多集群**: 集群间快速切换

## 贡献

欢迎提交 Issue 和 Pull Request！

### 开发环境要求

- Go 1.21+
- 有效的 Kubernetes 集群访问权限
- Make 工具

### 提交规范

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

更多详情请参考 [README.md](README.md)。
