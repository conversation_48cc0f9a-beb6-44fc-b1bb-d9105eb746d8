# 葵 (kui) Makefile

# 版本信息
VERSION ?= $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
COMMIT ?= $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_TIME ?= $(shell date -u '+%Y-%m-%d %H:%M:%S UTC')

# Go 相关变量
GOCMD = go
GOBUILD = $(GOCMD) build
GOCLEAN = $(GOCMD) clean
GOTEST = $(GOCMD) test
GOGET = $(GOCMD) get
GOMOD = $(GOCMD) mod

# 构建变量
BINARY_NAME = kui
BINARY_UNIX = $(BINARY_NAME)_unix
BINARY_WINDOWS = $(BINARY_NAME).exe
BINARY_DARWIN = $(BINARY_NAME)_darwin

# ldflags 用于注入版本信息
LDFLAGS = -ldflags "-X 'github.com/liyujun-dev/kui/cmd.version=$(VERSION)' \
                   -X 'github.com/liyujun-dev/kui/cmd.commit=$(COMMIT)' \
                   -X 'github.com/liyujun-dev/kui/cmd.buildTime=$(BUILD_TIME)'"

# 默认目标
.PHONY: all
all: test build

# 构建
.PHONY: build
build:
	$(GOBUILD) $(LDFLAGS) -o $(BINARY_NAME) -v

# 测试
.PHONY: test
test:
	$(GOTEST) -v ./...

# 清理
.PHONY: clean
clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_UNIX)
	rm -f $(BINARY_WINDOWS)
	rm -f $(BINARY_DARWIN)

# 运行
.PHONY: run
run:
	$(GOBUILD) $(LDFLAGS) -o $(BINARY_NAME) -v
	./$(BINARY_NAME)

# 运行 TUI
.PHONY: tui
tui:
	$(GOBUILD) $(LDFLAGS) -o $(BINARY_NAME) -v
	./$(BINARY_NAME)

# 依赖管理
.PHONY: deps
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# 交叉编译
.PHONY: build-linux
build-linux:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BINARY_UNIX) -v

.PHONY: build-windows
build-windows:
	CGO_ENABLED=0 GOOS=windows GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BINARY_WINDOWS) -v

.PHONY: build-darwin
build-darwin:
	CGO_ENABLED=0 GOOS=darwin GOARCH=amd64 $(GOBUILD) $(LDFLAGS) -o $(BINARY_DARWIN) -v

.PHONY: build-all
build-all: build-linux build-windows build-darwin

# 开发相关
.PHONY: dev
dev:
	$(GOBUILD) $(LDFLAGS) -o $(BINARY_NAME) -v
	./$(BINARY_NAME)

# 格式化代码
.PHONY: fmt
fmt:
	go fmt ./...

# 代码检查
.PHONY: vet
vet:
	go vet ./...

# 安装
.PHONY: install
install:
	$(GOBUILD) $(LDFLAGS) -o $(GOPATH)/bin/$(BINARY_NAME) -v

# 帮助
.PHONY: help
help:
	@echo "可用的 make 目标:"
	@echo "  build       - 构建二进制文件"
	@echo "  test        - 运行测试"
	@echo "  clean       - 清理构建文件"
	@echo "  run         - 构建并运行"
	@echo "  tui         - 构建并运行 TUI 模式"
	@echo "  deps        - 管理依赖"
	@echo "  build-all   - 交叉编译所有平台"
	@echo "  dev         - 开发模式运行"
	@echo "  fmt         - 格式化代码"
	@echo "  vet         - 代码检查"
	@echo "  install     - 安装到 GOPATH"
	@echo "  help        - 显示此帮助信息"
