package models

import (
	"testing"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// mockObject 实现 metav1.Object 接口用于测试
type mockObject struct {
	name        string
	namespace   string
	labels      map[string]string
	annotations map[string]string
	createdAt   metav1.Time
}

func (m *mockObject) GetName() string                                            { return m.name }
func (m *mockObject) SetName(name string)                                        { m.name = name }
func (m *mockObject) GetGenerateName() string                                    { return "" }
func (m *mockObject) SetGenerateName(name string)                                {}
func (m *mockObject) GetNamespace() string                                       { return m.namespace }
func (m *mockObject) SetNamespace(namespace string)                              { m.namespace = namespace }
func (m *mockObject) GetLabels() map[string]string                               { return m.labels }
func (m *mockObject) SetLabels(labels map[string]string)                         { m.labels = labels }
func (m *mockObject) GetAnnotations() map[string]string                          { return m.annotations }
func (m *mockObject) SetAnnotations(annotations map[string]string)               { m.annotations = annotations }
func (m *mockObject) GetCreationTimestamp() metav1.Time                          { return m.createdAt }
func (m *mockObject) SetCreationTimestamp(timestamp metav1.Time)                 { m.createdAt = timestamp }
func (m *mockObject) GetUID() string                                             { return "test-uid" }
func (m *mockObject) SetUID(uid string)                                          {}
func (m *mockObject) GetResourceVersion() string                                 { return "1" }
func (m *mockObject) SetResourceVersion(version string)                          {}
func (m *mockObject) GetGeneration() int64                                       { return 1 }
func (m *mockObject) SetGeneration(generation int64)                             {}
func (m *mockObject) GetSelfLink() string                                        { return "" }
func (m *mockObject) SetSelfLink(selfLink string)                                {}
func (m *mockObject) GetContinue() string                                        { return "" }
func (m *mockObject) SetContinue(c string)                                       {}
func (m *mockObject) GetRemainingItemCount() *int64                              { return nil }
func (m *mockObject) SetRemainingItemCount(c *int64)                             {}
func (m *mockObject) GetManagedFields() []metav1.ManagedFieldsEntry              { return nil }
func (m *mockObject) SetManagedFields(managedFields []metav1.ManagedFieldsEntry) {}
func (m *mockObject) GetOwnerReferences() []metav1.OwnerReference                { return nil }
func (m *mockObject) SetOwnerReferences(references []metav1.OwnerReference)      {}
func (m *mockObject) GetFinalizers() []string                                    { return nil }
func (m *mockObject) SetFinalizers(finalizers []string)                          {}
func (m *mockObject) GetDeletionTimestamp() *metav1.Time                         { return nil }
func (m *mockObject) SetDeletionTimestamp(timestamp *metav1.Time)                {}
func (m *mockObject) GetDeletionGracePeriodSeconds() *int64                      { return nil }
func (m *mockObject) SetDeletionGracePeriodSeconds(gracePeriodSeconds *int64)    {}

func TestNewResourceFromObject(t *testing.T) {
	// 创建测试对象
	createdAt := metav1.NewTime(time.Now().Add(-5 * time.Minute))
	obj := &mockObject{
		name:      "test-pod",
		namespace: "default",
		labels: map[string]string{
			"app": "test",
		},
		annotations: map[string]string{
			"description": "test pod",
		},
		createdAt: createdAt,
	}

	// 测试创建资源
	resource := NewResourceFromObject(obj, ResourceTypePod)

	// 验证结果
	if resource.Type != ResourceTypePod {
		t.Errorf("Expected type %s, got %s", ResourceTypePod, resource.Type)
	}

	if resource.Name != "test-pod" {
		t.Errorf("Expected name 'test-pod', got '%s'", resource.Name)
	}

	if resource.Namespace != "default" {
		t.Errorf("Expected namespace 'default', got '%s'", resource.Namespace)
	}

	if resource.Status != StatusUnknown {
		t.Errorf("Expected status %s, got %s", StatusUnknown, resource.Status)
	}

	if resource.Labels["app"] != "test" {
		t.Errorf("Expected label app='test', got '%s'", resource.Labels["app"])
	}

	if resource.Annotations["description"] != "test pod" {
		t.Errorf("Expected annotation description='test pod', got '%s'", resource.Annotations["description"])
	}

	if resource.CreatedAt != createdAt.Time {
		t.Errorf("Expected creation time %v, got %v", createdAt.Time, resource.CreatedAt)
	}
}

func TestFormatAge(t *testing.T) {
	tests := []struct {
		name     string
		age      time.Duration
		expected string
	}{
		{
			name:     "less than 1 minute",
			age:      30 * time.Second,
			expected: "< 1m",
		},
		{
			name:     "5 minutes",
			age:      5 * time.Minute,
			expected: "5m",
		},
		{
			name:     "2 hours",
			age:      2 * time.Hour,
			expected: "2h",
		},
		{
			name:     "3 days",
			age:      3 * 24 * time.Hour,
			expected: "3d",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			resource := &Resource{Age: tt.age}
			result := resource.FormatAge()
			if result != tt.expected {
				t.Errorf("Expected %s, got %s", tt.expected, result)
			}
		})
	}
}
