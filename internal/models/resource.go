package models

import (
	"fmt"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// ResourceType 资源类型枚举
type ResourceType string

const (
	ResourceTypePod                   ResourceType = "Pod"
	ResourceTypeService               ResourceType = "Service"
	ResourceTypeDeployment            ResourceType = "Deployment"
	ResourceTypeReplicaSet            ResourceType = "ReplicaSet"
	ResourceTypeNamespace             ResourceType = "Namespace"
	ResourceTypeNode                  ResourceType = "Node"
	ResourceTypeConfigMap             ResourceType = "ConfigMap"
	ResourceTypeSecret                ResourceType = "Secret"
	ResourceTypePersistentVolume      ResourceType = "PersistentVolume"
	ResourceTypePersistentVolumeClaim ResourceType = "PersistentVolumeClaim"
)

// ResourceStatus 资源状态
type ResourceStatus string

const (
	StatusRunning     ResourceStatus = "Running"
	StatusPending     ResourceStatus = "Pending"
	StatusFailed      ResourceStatus = "Failed"
	StatusSucceeded   ResourceStatus = "Succeeded"
	StatusUnknown     ResourceStatus = "Unknown"
	StatusTerminating ResourceStatus = "Terminating"
)

// Resource 通用资源结构
type Resource struct {
	Type        ResourceType      `json:"type"`
	Name        string            `json:"name"`
	Namespace   string            `json:"namespace"`
	Status      ResourceStatus    `json:"status"`
	Age         time.Duration     `json:"age"`
	Labels      map[string]string `json:"labels"`
	Annotations map[string]string `json:"annotations"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
}

// PodResource Pod 特定信息
type PodResource struct {
	Resource
	Ready      string          `json:"ready"` // 例如: "1/1"
	Restarts   int32           `json:"restarts"`
	NodeName   string          `json:"node_name"`
	PodIP      string          `json:"pod_ip"`
	Containers []ContainerInfo `json:"containers"`
}

// ContainerInfo 容器信息
type ContainerInfo struct {
	Name     string `json:"name"`
	Image    string `json:"image"`
	Ready    bool   `json:"ready"`
	Restarts int32  `json:"restarts"`
}

// ServiceResource Service 特定信息
type ServiceResource struct {
	Resource
	Type       string            `json:"service_type"`
	ClusterIP  string            `json:"cluster_ip"`
	ExternalIP string            `json:"external_ip"`
	Ports      []string          `json:"ports"`
	Selector   map[string]string `json:"selector"`
}

// DeploymentResource Deployment 特定信息
type DeploymentResource struct {
	Resource
	Ready     string `json:"ready"` // 例如: "3/3"
	UpToDate  int32  `json:"up_to_date"`
	Available int32  `json:"available"`
	Replicas  int32  `json:"replicas"`
	Strategy  string `json:"strategy"`
}

// NodeResource Node 特定信息
type NodeResource struct {
	Resource
	Ready         bool     `json:"ready"`
	Version       string   `json:"version"`
	InternalIP    string   `json:"internal_ip"`
	ExternalIP    string   `json:"external_ip"`
	OSImage       string   `json:"os_image"`
	KernelVersion string   `json:"kernel_version"`
	Roles         []string `json:"roles"`
}

// NewResourceFromObject 从 Kubernetes 对象创建 Resource
func NewResourceFromObject(obj metav1.Object, resourceType ResourceType) *Resource {
	now := time.Now()
	age := now.Sub(obj.GetCreationTimestamp().Time)

	return &Resource{
		Type:        resourceType,
		Name:        obj.GetName(),
		Namespace:   obj.GetNamespace(),
		Status:      StatusUnknown, // 需要根据具体资源类型设置
		Age:         age,
		Labels:      obj.GetLabels(),
		Annotations: obj.GetAnnotations(),
		CreatedAt:   obj.GetCreationTimestamp().Time,
		UpdatedAt:   now,
	}
}

// FormatAge 格式化年龄显示
func (r *Resource) FormatAge() string {
	if r.Age < time.Minute {
		return "< 1m"
	} else if r.Age < time.Hour {
		return fmt.Sprintf("%.0fm", r.Age.Minutes())
	} else if r.Age < 24*time.Hour {
		return fmt.Sprintf("%.0fh", r.Age.Hours())
	} else {
		return fmt.Sprintf("%.0fd", r.Age.Hours()/24)
	}
}
