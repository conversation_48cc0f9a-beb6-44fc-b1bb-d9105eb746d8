package config

import (
	"os"
	"path/filepath"

	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"
)

// Config 应用配置结构
type Config struct {
	KubeConfig     string
	CurrentContext string
	Namespace      string
	RefreshRate    int // 刷新间隔（秒）
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		KubeConfig:  getDefaultKubeConfigPath(),
		Namespace:   "default",
		RefreshRate: 5,
	}
}

// getDefaultKubeConfigPath 获取默认的 kubeconfig 路径
func getDefaultKubeConfigPath() string {
	if kubeconfig := os.Getenv("KUBECONFIG"); kubeconfig != "" {
		return kubeconfig
	}

	if home := homedir.HomeDir(); home != "" {
		return filepath.Join(home, ".kube", "config")
	}

	return ""
}

// LoadKubeConfig 加载 Kubernetes 配置
func (c *Config) LoadKubeConfig() (*rest.Config, error) {
	// 如果在集群内运行，使用集群内配置
	if config, err := rest.InClusterConfig(); err == nil {
		return config, nil
	}

	// 使用 kubeconfig 文件
	config, err := clientcmd.BuildConfigFromFlags("", c.KubeConfig)
	if err != nil {
		return nil, err
	}

	return config, nil
}

// GetCurrentContext 获取当前上下文
func (c *Config) GetCurrentContext() (string, error) {
	if c.KubeConfig == "" {
		return "", nil
	}

	config, err := clientcmd.LoadFromFile(c.KubeConfig)
	if err != nil {
		return "", err
	}

	return config.CurrentContext, nil
}

// ListContexts 列出所有可用的上下文
func (c *Config) ListContexts() ([]string, error) {
	if c.KubeConfig == "" {
		return nil, nil
	}

	config, err := clientcmd.LoadFromFile(c.KubeConfig)
	if err != nil {
		return nil, err
	}

	var contexts []string
	for name := range config.Contexts {
		contexts = append(contexts, name)
	}

	return contexts, nil
}
