package tui

import (
	"fmt"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/liyujun-dev/kui/internal/tui/views"
)

// App TUI 应用结构
type App struct {
	program *tea.Program
	model   tea.Model
}

// NewApp 创建新的 TUI 应用
func NewApp() (*App, error) {
	// 尝试创建资源列表模型
	resourceListModel, err := views.NewResourceListModel()
	if err != nil {
		// 如果创建失败，使用简单模型
		model := &SimpleModel{ready: false}
		program := tea.NewProgram(
			model,
			tea.WithAltScreen(),
			tea.WithMouseCellMotion(),
		)
		return &App{
			program: program,
			model:   model,
		}, nil
	}

	program := tea.NewProgram(
		resourceListModel,
		tea.WithAltScreen(),
		tea.WithMouseCellMotion(),
	)

	return &App{
		program: program,
		model:   resourceListModel,
	}, nil
}

// Run 运行 TUI 应用
func (a *App) Run() error {
	_, err := a.program.Run()
	return err
}

// SimpleModel 简单模型（当无法连接 K8s 时使用）
type SimpleModel struct {
	width  int
	height int
	ready  bool
}

// Init 初始化模型
func (m *SimpleModel) Init() tea.Cmd {
	return nil
}

// Update 更新模型
func (m *SimpleModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		m.ready = true
		return m, nil

	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			return m, tea.Quit
		}
	}

	return m, nil
}

// View 渲染视图
func (m *SimpleModel) View() string {
	if !m.ready {
		return "初始化中..."
	}

	header := "葵 (kui) - Kubernetes TUI 管理工具\n"
	footer := "\n按 'q' 或 Ctrl+C 退出"

	content := fmt.Sprintf(
		"终端大小: %d x %d\n\n"+
			"无法连接到 Kubernetes 集群\n"+
			"请检查:\n"+
			"• kubeconfig 配置是否正确\n"+
			"• 集群是否可访问\n"+
			"• 网络连接是否正常\n\n"+
			"配置文件位置: ~/.kube/config\n"+
			"或设置环境变量: KUBECONFIG",
		m.width, m.height,
	)

	return header + content + footer
}
