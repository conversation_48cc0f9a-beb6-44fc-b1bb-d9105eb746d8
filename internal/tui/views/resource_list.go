package views

import (
	"context"
	"fmt"
	"strings"
	"time"

	tea "github.com/charmbracelet/bubbletea"
	"github.com/charmbracelet/lipgloss"
	"github.com/liyujun-dev/kui/internal/config"
	"github.com/liyujun-dev/kui/internal/k8s"
	"github.com/liyujun-dev/kui/internal/models"
)

// ResourceListModel 资源列表模型
type ResourceListModel struct {
	width        int
	height       int
	client       *k8s.Client
	resourceType models.ResourceType
	namespace    string
	pods         []*models.PodResource
	services     []*models.ServiceResource
	deployments  []*models.DeploymentResource
	nodes        []*models.NodeResource
	namespaces   []*models.Resource
	cursor       int
	loading      bool
	err          error
	lastUpdate   time.Time
}

// NewResourceListModel 创建新的资源列表模型
func NewResourceListModel() (*ResourceListModel, error) {
	cfg := config.DefaultConfig()
	client, err := k8s.NewClient(cfg)
	if err != nil {
		return nil, fmt.Errorf("创建 K8s 客户端失败: %w", err)
	}

	return &ResourceListModel{
		client:       client,
		resourceType: models.ResourceTypePod,
		namespace:    "default",
		loading:      true,
	}, nil
}

// Init 初始化
func (m *ResourceListModel) Init() tea.Cmd {
	return m.refreshResources()
}

// Update 更新模型
func (m *ResourceListModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	switch msg := msg.(type) {
	case tea.WindowSizeMsg:
		m.width = msg.Width
		m.height = msg.Height
		return m, nil

	case tea.KeyMsg:
		switch msg.String() {
		case "ctrl+c", "q":
			return m, tea.Quit
		case "up", "k":
			if m.cursor > 0 {
				m.cursor--
			}
		case "down", "j":
			maxCursor := m.getMaxCursor()
			if m.cursor < maxCursor {
				m.cursor++
			}
		case "1":
			m.resourceType = models.ResourceTypePod
			m.cursor = 0
			return m, m.refreshResources()
		case "2":
			m.resourceType = models.ResourceTypeService
			m.cursor = 0
			return m, m.refreshResources()
		case "3":
			m.resourceType = models.ResourceTypeDeployment
			m.cursor = 0
			return m, m.refreshResources()
		case "4":
			m.resourceType = models.ResourceTypeNode
			m.cursor = 0
			return m, m.refreshResources()
		case "5":
			m.resourceType = models.ResourceTypeNamespace
			m.cursor = 0
			return m, m.refreshResources()
		case "r":
			m.loading = true
			return m, m.refreshResources()
		}

	case refreshMsg:
		m.loading = false
		m.lastUpdate = time.Now()
		if msg.err != nil {
			m.err = msg.err
		} else {
			m.err = nil
			m.pods = msg.pods
			m.services = msg.services
			m.deployments = msg.deployments
			m.nodes = msg.nodes
			m.namespaces = msg.namespaces
		}
		return m, nil
	}

	return m, nil
}

// View 渲染视图
func (m *ResourceListModel) View() string {
	if m.width == 0 || m.height == 0 {
		return "初始化中..."
	}

	var content strings.Builder

	// 标题栏
	title := m.renderTitle()
	content.WriteString(title)
	content.WriteString("\n")

	// 导航栏
	nav := m.renderNavigation()
	content.WriteString(nav)
	content.WriteString("\n")

	// 资源列表
	if m.loading {
		content.WriteString("加载中...")
	} else if m.err != nil {
		content.WriteString(fmt.Sprintf("错误: %v", m.err))
	} else {
		resourceList := m.renderResourceList()
		content.WriteString(resourceList)
	}

	// 状态栏
	content.WriteString("\n")
	status := m.renderStatusBar()
	content.WriteString(status)

	return content.String()
}

// renderTitle 渲染标题
func (m *ResourceListModel) renderTitle() string {
	titleStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("#FFFFFF")).
		Background(lipgloss.Color("#5C7E3E")).
		Padding(0, 1).
		Width(m.width)

	return titleStyle.Render("葵 (kui) - Kubernetes 资源管理")
}

// renderNavigation 渲染导航栏
func (m *ResourceListModel) renderNavigation() string {
	items := []struct {
		key  string
		name string
		rt   models.ResourceType
	}{
		{"1", "Pods", models.ResourceTypePod},
		{"2", "Services", models.ResourceTypeService},
		{"3", "Deployments", models.ResourceTypeDeployment},
		{"4", "Nodes", models.ResourceTypeNode},
		{"5", "Namespaces", models.ResourceTypeNamespace},
	}

	var navItems []string
	for _, item := range items {
		if item.rt == m.resourceType {
			// 选中状态：深色背景，白色文字
			style := lipgloss.NewStyle().
				Bold(true).
				Foreground(lipgloss.Color("#FFFFFF")).
				Background(lipgloss.Color("#3C5AA6")).
				Padding(0, 1)
			navItems = append(navItems, style.Render(fmt.Sprintf("[%s] %s", item.key, item.name)))
		} else {
			// 未选中状态：浅色文字
			style := lipgloss.NewStyle().
				Foreground(lipgloss.Color("#8B949E")).
				Padding(0, 1)
			navItems = append(navItems, style.Render(fmt.Sprintf("[%s] %s", item.key, item.name)))
		}
	}

	// 添加分隔线
	navBar := strings.Join(navItems, " ")
	separator := lipgloss.NewStyle().
		Foreground(lipgloss.Color("#30363D")).
		Render(strings.Repeat("─", m.width))

	return navBar + "\n" + separator
}

// renderResourceList 渲染资源列表
func (m *ResourceListModel) renderResourceList() string {
	switch m.resourceType {
	case models.ResourceTypePod:
		return m.renderPodList()
	case models.ResourceTypeService:
		return m.renderServiceList()
	case models.ResourceTypeDeployment:
		return m.renderDeploymentList()
	case models.ResourceTypeNode:
		return m.renderNodeList()
	case models.ResourceTypeNamespace:
		return m.renderNamespaceList()
	default:
		return "未知资源类型"
	}
}

// renderPodList 渲染 Pod 列表
func (m *ResourceListModel) renderPodList() string {
	if len(m.pods) == 0 {
		emptyStyle := lipgloss.NewStyle().
			Foreground(lipgloss.Color("#8B949E")).
			Italic(true).
			Padding(2, 0)
		return emptyStyle.Render("没有找到 Pod 资源")
	}

	var lines []string

	// 表头样式
	headerStyle := lipgloss.NewStyle().
		Bold(true).
		Foreground(lipgloss.Color("#F0F6FC")).
		Background(lipgloss.Color("#21262D")).
		Padding(0, 1)

	header := fmt.Sprintf("%-30s %-10s %-12s %-10s %-8s", "NAME", "READY", "STATUS", "RESTARTS", "AGE")
	lines = append(lines, headerStyle.Render(header))

	for i, pod := range m.pods {
		var style lipgloss.Style
		if i == m.cursor {
			// 选中行样式 - lazygit风格的高亮
			style = lipgloss.NewStyle().
				Background(lipgloss.Color("#264F78")).
				Foreground(lipgloss.Color("#FFFFFF")).
				Padding(0, 1)
		} else {
			// 普通行样式
			style = lipgloss.NewStyle().
				Foreground(lipgloss.Color("#E6EDF3")).
				Padding(0, 1)
		}

		// 根据状态设置颜色
		statusColor := getStatusColor(string(pod.Status))
		statusStyle := lipgloss.NewStyle().Foreground(statusColor)

		line := fmt.Sprintf("%-30s %-10s %-12s %-10d %-8s",
			truncate(pod.Name, 30),
			pod.Ready,
			statusStyle.Render(string(pod.Status)),
			pod.Restarts,
			pod.FormatAge(),
		)
		lines = append(lines, style.Render(line))
	}

	return strings.Join(lines, "\n")
}

// renderServiceList 渲染 Service 列表
func (m *ResourceListModel) renderServiceList() string {
	if len(m.services) == 0 {
		return "没有找到 Service 资源"
	}

	var lines []string
	header := fmt.Sprintf("%-30s %-15s %-15s %-20s %-10s", "NAME", "TYPE", "CLUSTER-IP", "EXTERNAL-IP", "AGE")
	lines = append(lines, header)
	lines = append(lines, strings.Repeat("-", len(header)))

	for i, service := range m.services {
		style := lipgloss.NewStyle()
		if i == m.cursor {
			style = style.Background(lipgloss.Color("240"))
		}

		line := fmt.Sprintf("%-30s %-15s %-15s %-20s %-10s",
			truncate(service.Name, 30),
			service.Type,
			service.ClusterIP,
			truncate(service.ExternalIP, 20),
			service.FormatAge(),
		)
		lines = append(lines, style.Render(line))
	}

	return strings.Join(lines, "\n")
}

// renderDeploymentList 渲染 Deployment 列表
func (m *ResourceListModel) renderDeploymentList() string {
	if len(m.deployments) == 0 {
		return "没有找到 Deployment 资源"
	}

	var lines []string
	header := fmt.Sprintf("%-30s %-10s %-10s %-10s %-10s", "NAME", "READY", "UP-TO-DATE", "AVAILABLE", "AGE")
	lines = append(lines, header)
	lines = append(lines, strings.Repeat("-", len(header)))

	for i, deployment := range m.deployments {
		style := lipgloss.NewStyle()
		if i == m.cursor {
			style = style.Background(lipgloss.Color("240"))
		}

		line := fmt.Sprintf("%-30s %-10s %-10d %-10d %-10s",
			truncate(deployment.Name, 30),
			deployment.Ready,
			deployment.UpToDate,
			deployment.Available,
			deployment.FormatAge(),
		)
		lines = append(lines, style.Render(line))
	}

	return strings.Join(lines, "\n")
}

// renderNodeList 渲染 Node 列表
func (m *ResourceListModel) renderNodeList() string {
	if len(m.nodes) == 0 {
		return "没有找到 Node 资源"
	}

	var lines []string
	header := fmt.Sprintf("%-30s %-10s %-15s %-15s %-10s", "NAME", "STATUS", "ROLES", "VERSION", "AGE")
	lines = append(lines, header)
	lines = append(lines, strings.Repeat("-", len(header)))

	for i, node := range m.nodes {
		style := lipgloss.NewStyle()
		if i == m.cursor {
			style = style.Background(lipgloss.Color("240"))
		}

		status := "Ready"
		if !node.Ready {
			status = "NotReady"
		}

		roles := strings.Join(node.Roles, ",")
		if roles == "" {
			roles = "<none>"
		}

		line := fmt.Sprintf("%-30s %-10s %-15s %-15s %-10s",
			truncate(node.Name, 30),
			status,
			truncate(roles, 15),
			truncate(node.Version, 15),
			node.FormatAge(),
		)
		lines = append(lines, style.Render(line))
	}

	return strings.Join(lines, "\n")
}

// renderNamespaceList 渲染 Namespace 列表
func (m *ResourceListModel) renderNamespaceList() string {
	if len(m.namespaces) == 0 {
		return "没有找到 Namespace 资源"
	}

	var lines []string
	header := fmt.Sprintf("%-30s %-15s %-10s", "NAME", "STATUS", "AGE")
	lines = append(lines, header)
	lines = append(lines, strings.Repeat("-", len(header)))

	for i, namespace := range m.namespaces {
		style := lipgloss.NewStyle()
		if i == m.cursor {
			style = style.Background(lipgloss.Color("240"))
		}

		line := fmt.Sprintf("%-30s %-15s %-10s",
			truncate(namespace.Name, 30),
			string(namespace.Status),
			namespace.FormatAge(),
		)
		lines = append(lines, style.Render(line))
	}

	return strings.Join(lines, "\n")
}

// renderStatusBar 渲染状态栏
func (m *ResourceListModel) renderStatusBar() string {
	// 状态栏背景
	statusStyle := lipgloss.NewStyle().
		Foreground(lipgloss.Color("#E6EDF3")).
		Background(lipgloss.Color("#21262D")).
		Padding(0, 1).
		Width(m.width)

	var leftPart, rightPart string

	if m.err != nil {
		// 错误状态
		errorStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("#F85149"))
		leftPart = errorStyle.Render(fmt.Sprintf("错误: %v", m.err))
		rightPart = "按 'q' 退出"
	} else {
		count := m.getResourceCount()
		// 左侧信息
		nsStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("#79C0FF"))
		countStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("#3FB950"))
		timeStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("#8B949E"))

		leftPart = fmt.Sprintf("命名空间: %s | 资源数: %s | 最后更新: %s",
			nsStyle.Render(m.namespace),
			countStyle.Render(fmt.Sprintf("%d", count)),
			timeStyle.Render(m.lastUpdate.Format("15:04:05")))

		// 右侧快捷键提示
		keyStyle := lipgloss.NewStyle().Foreground(lipgloss.Color("#F79000"))
		rightPart = fmt.Sprintf("%s 刷新 | %s 退出",
			keyStyle.Render("r"),
			keyStyle.Render("q"))
	}

	// 计算空白填充
	contentLen := lipgloss.Width(leftPart) + lipgloss.Width(rightPart)
	padding := ""
	if m.width > contentLen {
		padding = strings.Repeat(" ", m.width-contentLen-2) // -2 for padding
	}

	content := leftPart + padding + rightPart
	return statusStyle.Render(content)
}

// getMaxCursor 获取最大光标位置
func (m *ResourceListModel) getMaxCursor() int {
	count := m.getResourceCount()
	if count > 0 {
		return count - 1
	}
	return 0
}

// getResourceCount 获取当前资源数量
func (m *ResourceListModel) getResourceCount() int {
	switch m.resourceType {
	case models.ResourceTypePod:
		return len(m.pods)
	case models.ResourceTypeService:
		return len(m.services)
	case models.ResourceTypeDeployment:
		return len(m.deployments)
	case models.ResourceTypeNode:
		return len(m.nodes)
	case models.ResourceTypeNamespace:
		return len(m.namespaces)
	default:
		return 0
	}
}

// refreshMsg 刷新消息
type refreshMsg struct {
	pods        []*models.PodResource
	services    []*models.ServiceResource
	deployments []*models.DeploymentResource
	nodes       []*models.NodeResource
	namespaces  []*models.Resource
	err         error
}

// refreshResources 刷新资源
func (m *ResourceListModel) refreshResources() tea.Cmd {
	return func() tea.Msg {
		ctx := context.Background()

		var pods []*models.PodResource
		var services []*models.ServiceResource
		var deployments []*models.DeploymentResource
		var nodes []*models.NodeResource
		var namespaces []*models.Resource
		var err error

		switch m.resourceType {
		case models.ResourceTypePod:
			pods, err = m.client.ListPods(ctx, m.namespace)
		case models.ResourceTypeService:
			services, err = m.client.ListServices(ctx, m.namespace)
		case models.ResourceTypeDeployment:
			deployments, err = m.client.ListDeployments(ctx, m.namespace)
		case models.ResourceTypeNode:
			nodes, err = m.client.ListNodes(ctx)
		case models.ResourceTypeNamespace:
			namespaces, err = m.client.ListNamespaces(ctx)
		}

		return refreshMsg{
			pods:        pods,
			services:    services,
			deployments: deployments,
			nodes:       nodes,
			namespaces:  namespaces,
			err:         err,
		}
	}
}

// truncate 截断字符串
func truncate(s string, length int) string {
	if len(s) <= length {
		return s
	}
	return s[:length-3] + "..."
}

// getStatusColor 根据状态返回对应的颜色
func getStatusColor(status string) lipgloss.Color {
	switch status {
	case "Running":
		return lipgloss.Color("#3FB950") // 绿色
	case "Pending":
		return lipgloss.Color("#D29922") // 黄色
	case "Failed", "Error":
		return lipgloss.Color("#F85149") // 红色
	case "Succeeded":
		return lipgloss.Color("#3FB950") // 绿色
	case "Terminating":
		return lipgloss.Color("#F79000") // 橙色
	case "Ready":
		return lipgloss.Color("#3FB950") // 绿色
	case "NotReady":
		return lipgloss.Color("#F85149") // 红色
	default:
		return lipgloss.Color("#8B949E") // 灰色
	}
}
