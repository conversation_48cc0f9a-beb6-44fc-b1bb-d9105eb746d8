package k8s

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/liyujun-dev/kui/internal/models"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// ResourceProvider 资源提供者接口
type ResourceProvider interface {
	ListPods(ctx context.Context, namespace string) ([]*models.PodResource, error)
	ListServices(ctx context.Context, namespace string) ([]*models.ServiceResource, error)
	ListDeployments(ctx context.Context, namespace string) ([]*models.DeploymentResource, error)
	ListNodes(ctx context.Context) ([]*models.NodeResource, error)
	ListNamespaces(ctx context.Context) ([]*models.Resource, error)
}

// Ensure Client implements ResourceProvider
var _ ResourceProvider = (*Client)(nil)

// ListPods 列出 Pod 资源
func (c *Client) ListPods(ctx context.Context, namespace string) ([]*models.PodResource, error) {
	if namespace == "" {
		namespace = c.namespace
	}

	pods, err := c.clientset.CoreV1().Pods(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取 Pod 列表失败: %w", err)
	}

	var podResources []*models.PodResource
	for _, pod := range pods.Items {
		podResource := c.convertPodToResource(&pod)
		podResources = append(podResources, podResource)
	}

	return podResources, nil
}

// ListServices 列出 Service 资源
func (c *Client) ListServices(ctx context.Context, namespace string) ([]*models.ServiceResource, error) {
	if namespace == "" {
		namespace = c.namespace
	}

	services, err := c.clientset.CoreV1().Services(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取 Service 列表失败: %w", err)
	}

	var serviceResources []*models.ServiceResource
	for _, service := range services.Items {
		serviceResource := c.convertServiceToResource(&service)
		serviceResources = append(serviceResources, serviceResource)
	}

	return serviceResources, nil
}

// ListDeployments 列出 Deployment 资源
func (c *Client) ListDeployments(ctx context.Context, namespace string) ([]*models.DeploymentResource, error) {
	if namespace == "" {
		namespace = c.namespace
	}

	deployments, err := c.clientset.AppsV1().Deployments(namespace).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取 Deployment 列表失败: %w", err)
	}

	var deploymentResources []*models.DeploymentResource
	for _, deployment := range deployments.Items {
		deploymentResource := c.convertDeploymentToResource(&deployment)
		deploymentResources = append(deploymentResources, deploymentResource)
	}

	return deploymentResources, nil
}

// ListNodes 列出 Node 资源
func (c *Client) ListNodes(ctx context.Context) ([]*models.NodeResource, error) {
	nodes, err := c.clientset.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取 Node 列表失败: %w", err)
	}

	var nodeResources []*models.NodeResource
	for _, node := range nodes.Items {
		nodeResource := c.convertNodeToResource(&node)
		nodeResources = append(nodeResources, nodeResource)
	}

	return nodeResources, nil
}

// ListNamespaces 列出 Namespace 资源
func (c *Client) ListNamespaces(ctx context.Context) ([]*models.Resource, error) {
	namespaces, err := c.clientset.CoreV1().Namespaces().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("获取 Namespace 列表失败: %w", err)
	}

	var namespaceResources []*models.Resource
	for _, namespace := range namespaces.Items {
		resource := models.NewResourceFromObject(&namespace, models.ResourceTypeNamespace)
		resource.Status = models.ResourceStatus(namespace.Status.Phase)
		namespaceResources = append(namespaceResources, resource)
	}

	return namespaceResources, nil
}

// convertPodToResource 转换 Pod 到 PodResource
func (c *Client) convertPodToResource(pod *corev1.Pod) *models.PodResource {
	baseResource := models.NewResourceFromObject(pod, models.ResourceTypePod)
	baseResource.Status = models.ResourceStatus(pod.Status.Phase)

	// 计算 Ready 状态
	readyContainers := 0
	totalContainers := len(pod.Status.ContainerStatuses)
	for _, status := range pod.Status.ContainerStatuses {
		if status.Ready {
			readyContainers++
		}
	}

	// 计算重启次数
	var restarts int32
	for _, status := range pod.Status.ContainerStatuses {
		restarts += status.RestartCount
	}

	// 转换容器信息
	var containers []models.ContainerInfo
	for _, status := range pod.Status.ContainerStatuses {
		containers = append(containers, models.ContainerInfo{
			Name:     status.Name,
			Image:    status.Image,
			Ready:    status.Ready,
			Restarts: status.RestartCount,
		})
	}

	return &models.PodResource{
		Resource:   *baseResource,
		Ready:      fmt.Sprintf("%d/%d", readyContainers, totalContainers),
		Restarts:   restarts,
		NodeName:   pod.Spec.NodeName,
		PodIP:      pod.Status.PodIP,
		Containers: containers,
	}
}

// convertServiceToResource 转换 Service 到 ServiceResource
func (c *Client) convertServiceToResource(service *corev1.Service) *models.ServiceResource {
	baseResource := models.NewResourceFromObject(service, models.ResourceTypeService)
	baseResource.Status = models.StatusRunning // Service 通常是 Running 状态

	// 转换端口信息
	var ports []string
	for _, port := range service.Spec.Ports {
		portStr := strconv.Itoa(int(port.Port))
		if port.Protocol != corev1.ProtocolTCP {
			portStr += "/" + string(port.Protocol)
		}
		ports = append(ports, portStr)
	}

	// 获取外部 IP
	externalIP := "<none>"
	if len(service.Status.LoadBalancer.Ingress) > 0 {
		if service.Status.LoadBalancer.Ingress[0].IP != "" {
			externalIP = service.Status.LoadBalancer.Ingress[0].IP
		} else if service.Status.LoadBalancer.Ingress[0].Hostname != "" {
			externalIP = service.Status.LoadBalancer.Ingress[0].Hostname
		}
	}

	return &models.ServiceResource{
		Resource:   *baseResource,
		Type:       string(service.Spec.Type),
		ClusterIP:  service.Spec.ClusterIP,
		ExternalIP: externalIP,
		Ports:      ports,
		Selector:   service.Spec.Selector,
	}
}

// convertDeploymentToResource 转换 Deployment 到 DeploymentResource
func (c *Client) convertDeploymentToResource(deployment *appsv1.Deployment) *models.DeploymentResource {
	baseResource := models.NewResourceFromObject(deployment, models.ResourceTypeDeployment)

	// 根据状态设置资源状态
	if deployment.Status.ReadyReplicas == deployment.Status.Replicas {
		baseResource.Status = models.StatusRunning
	} else {
		baseResource.Status = models.StatusPending
	}

	replicas := int32(0)
	if deployment.Spec.Replicas != nil {
		replicas = *deployment.Spec.Replicas
	}

	return &models.DeploymentResource{
		Resource:  *baseResource,
		Ready:     fmt.Sprintf("%d/%d", deployment.Status.ReadyReplicas, replicas),
		UpToDate:  deployment.Status.UpdatedReplicas,
		Available: deployment.Status.AvailableReplicas,
		Replicas:  replicas,
		Strategy:  string(deployment.Spec.Strategy.Type),
	}
}

// convertNodeToResource 转换 Node 到 NodeResource
func (c *Client) convertNodeToResource(node *corev1.Node) *models.NodeResource {
	baseResource := models.NewResourceFromObject(node, models.ResourceTypeNode)

	// 检查节点是否就绪
	ready := false
	for _, condition := range node.Status.Conditions {
		if condition.Type == corev1.NodeReady && condition.Status == corev1.ConditionTrue {
			ready = true
			break
		}
	}

	if ready {
		baseResource.Status = models.StatusRunning
	} else {
		baseResource.Status = models.StatusFailed
	}

	// 获取 IP 地址
	var internalIP, externalIP string
	for _, address := range node.Status.Addresses {
		switch address.Type {
		case corev1.NodeInternalIP:
			internalIP = address.Address
		case corev1.NodeExternalIP:
			externalIP = address.Address
		}
	}

	// 获取节点角色
	var roles []string
	for label := range node.Labels {
		if strings.HasPrefix(label, "node-role.kubernetes.io/") {
			role := strings.TrimPrefix(label, "node-role.kubernetes.io/")
			if role == "" {
				role = "master"
			}
			roles = append(roles, role)
		}
	}

	return &models.NodeResource{
		Resource:      *baseResource,
		Ready:         ready,
		Version:       node.Status.NodeInfo.KubeletVersion,
		InternalIP:    internalIP,
		ExternalIP:    externalIP,
		OSImage:       node.Status.NodeInfo.OSImage,
		KernelVersion: node.Status.NodeInfo.KernelVersion,
		Roles:         roles,
	}
}
